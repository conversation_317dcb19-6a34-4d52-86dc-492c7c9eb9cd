package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.ReportDataDTO;
import com.dwdo.hotdesk.dto.request.ReportRequestDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.util.NullUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionFilterService filterService;

    public ResponseEntity<Resource> generateReportExcel(ReportRequestDTO request) {
        log.info("[ReportService] Starting Excel report generation with filters: startDate={}, endDate={}, status={}",
                request.getStartDate(), request.getEndDate(), request.getStatus());

        try {
            if (request.getStartDate() == null || request.getStartDate().trim().isEmpty()) {
                throw new CustomBadRequestException(400, "Bad Request", "Start date is required");
            }
            if (request.getEndDate() == null || request.getEndDate().trim().isEmpty()) {
                throw new CustomBadRequestException(400, "Bad Request", "End date is required");
            }

            LocalDateTime startDate = filterService.parseStartDate(request.getStartDate());
            LocalDateTime endDate = filterService.parseEndDate(request.getEndDate());

            if (startDate.isAfter(endDate)) {
                throw new CustomBadRequestException(400, "Bad Request",
                        "Start date cannot be after end date");
            }

            String currentUser = SecurityUtil.getCurrentUserLogin()
                    .orElseThrow(() -> new CustomBadRequestException(401, "Authentication Error",
                            "User not authenticated"));

            Specification<Submission> spec = buildReportSpecification(request.getStatus(), startDate, endDate);

            List<Submission> submissions = submissionRepository.findAll(spec);

            log.info("[ReportService] Found {} submissions matching criteria", submissions.size());

            if (submissions.isEmpty()) {
                throw new CustomBadRequestException(404, "No Data Found",
                        "No submissions found matching the specified criteria");
            }

            byte[] excelData = createReportExcelFile(submissions, currentUser);

            ByteArrayResource resource = new ByteArrayResource(excelData);

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("submission_report_%s.xlsx", timestamp);

            log.info("[ReportService] Excel report generated successfully with {} records", submissions.size());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(resource.contentLength())
                    .body(resource);

        } catch (CustomBadRequestException e) {
            log.error("[ReportService] Validation error: {}", e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("[ReportService] IO error while creating Excel file", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                    "Failed to create Excel file due to IO error: " + e.getMessage());
        } catch (Exception e) {
            log.error("[ReportService] Unexpected error generating Excel report", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                    "An unexpected error occurred while generating Excel report: " + e.getMessage());
        }
    }

    private Specification<Submission> buildReportSpecification(String status, LocalDateTime startDate, LocalDateTime endDate) {
        Specification<Submission> spec = Specification.where(null);

        if (status != null && !status.trim().isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), status));
            log.info("[ReportService] Filtering by status: {}", status);
        } else {
            log.info("[ReportService] No status filter applied - gathering all submissions within date range");
        }

        if (startDate != null && endDate != null) {
            spec = spec.and((root, query, cb) -> cb.between(root.get("createdAt"), startDate, endDate));
            log.info("[ReportService] Filtering by date range: {} to {}", startDate, endDate);
        } else if (startDate != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("createdAt"), startDate));
            log.info("[ReportService] Filtering from start date: {}", startDate);
        } else if (endDate != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("createdAt"), endDate));
            log.info("[ReportService] Filtering up to end date: {}", endDate);
        }

        return spec;
    }

    private byte[] createReportExcelFile(List<Submission> submissions, String generatedBy) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Submission Report");

            sheet.setDisplayGridlines(false);

            CellStyle dateStyle = workbook.createCellStyle();
            CreationHelper createHelper = workbook.getCreationHelper();
            dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-MMM-yyyy"));

            CellStyle generalStyle = workbook.createCellStyle();
            generalStyle.setDataFormat(createHelper.createDataFormat().getFormat("General"));

            createReportMetadata(workbook, sheet, generatedBy);

            createTableHeaderRow(workbook, sheet);

            createTableDataRows(sheet, submissions, generalStyle);

            for (int i = 0; i < 22; i++) { 
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    private void createReportMetadata(Workbook workbook, Sheet sheet, String generatedBy) {
        String reportDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss"));

        CellStyle labelStyle = workbook.createCellStyle();
        Font labelFont = workbook.createFont();
        labelFont.setBold(true);
        labelStyle.setFont(labelFont);

        CellStyle valueStyle = workbook.createCellStyle();

        Row row1 = sheet.createRow(0);
        Cell labelCell1 = row1.createCell(0);
        labelCell1.setCellValue("Report Date:");
        labelCell1.setCellStyle(labelStyle);

        Cell valueCell1 = row1.createCell(1);
        valueCell1.setCellValue(reportDate);
        valueCell1.setCellStyle(valueStyle);

        Row row2 = sheet.createRow(1);
        Cell labelCell2 = row2.createCell(0);
        labelCell2.setCellValue("Generated By:");
        labelCell2.setCellStyle(labelStyle);

        Cell valueCell2 = row2.createCell(1);
        valueCell2.setCellValue(generatedBy);
        valueCell2.setCellStyle(valueStyle);
    }

    private void createTableHeaderRow(Workbook workbook, Sheet sheet) {
        Row headerRow = sheet.createRow(3); 

        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        String[] headers = {
                "ID", "Reference Number", "Submitter Name", "Submitter Job", "Status",
                "NIP", "Name", "Grade", "Payment Type", "Amount", "Description",
                "Month of Process", "Year of Process", "Directorate", "SLIK", "Sanction",
                "Termination Date", "Eligible", "Payment Date", "Remarks"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void createTableDataRows(Sheet sheet, List<Submission> submissions, CellStyle generalStyle) {
        int rowNum = 4; 

        CellStyle dataStyle = sheet.getWorkbook().createCellStyle();
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        CellStyle amountStyle = sheet.getWorkbook().createCellStyle();
        amountStyle.cloneStyleFrom(generalStyle);
        amountStyle.setBorderTop(BorderStyle.THIN);
        amountStyle.setBorderBottom(BorderStyle.THIN);
        amountStyle.setBorderLeft(BorderStyle.THIN);
        amountStyle.setBorderRight(BorderStyle.THIN);

        for (Submission submission : submissions) {
            Row row = sheet.createRow(rowNum++);

            createBorderedCell(row, 0, submission.getId().toString(), dataStyle);
            createBorderedCell(row, 1, submission.getReferenceNumber(), dataStyle);
            createBorderedCell(row, 2, submission.getSubmitterName(), dataStyle);
            createBorderedCell(row, 3, submission.getSubmitterJob(), dataStyle);
            createBorderedCell(row, 4, submission.getStatus(), dataStyle);
            createBorderedCell(row, 5, submission.getNip(), dataStyle);
            createBorderedCell(row, 6, submission.getName(), dataStyle);
            createBorderedCell(row, 7, submission.getGrade(), dataStyle);
            createBorderedCell(row, 8, submission.getPaymentType(), dataStyle);

            Cell amountCell = row.createCell(9);
            amountCell.setCellValue(submission.getAmount().doubleValue());
            amountCell.setCellStyle(amountStyle);

            createBorderedCell(row, 10, submission.getDescription(), dataStyle);
            createBorderedCell(row, 11, submission.getMonthOfProcess(), dataStyle);
            createBorderedCell(row, 12, submission.getYearOfProcess(), dataStyle);
            createBorderedCell(row, 13, submission.getDirectorate(), dataStyle);
            createBorderedCell(row, 14, submission.getSlik(), dataStyle);
            createBorderedCell(row, 15, submission.getSanction(), dataStyle);
            createBorderedCell(row, 16, submission.getTerminationDate(), dataStyle);
            createBorderedCell(row, 17, submission.getEligible() != null ? submission.getEligible().toString() : "-", dataStyle);

            String paymentDateStr = NullUtil.toDisplayString(submission.getPaymentDate());
            createBorderedCell(row, 18, paymentDateStr, dataStyle);

            String remarksStr = NullUtil.toDisplayString(submission.getRemarks());
            createBorderedCell(row, 19, remarksStr, dataStyle);
        }
    }

    private void createBorderedCell(Row row, int columnIndex, String value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(value != null ? value : "-");
        cell.setCellStyle(style);
    }
}
